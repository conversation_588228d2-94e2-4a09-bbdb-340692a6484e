<?php

namespace App\Filament\Resources\Suppliers\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class SupplierForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->label(__('Name'))
                    ->required(),

                TextInput::make('email')
                    ->label(__('Email'))
                    ->email()
                    ->required(),

                TextInput::make('phone')
                    ->label(__('Phone'))
                    ->tel()
                    ->required(),

                Textarea::make('address')
                    ->label(__('Address'))
                    ->rows(3),

                BelongsToSelect::make('products')
                    ->label(__('Products'))
                    ->relationship('products', 'name')
                    ->multiple()
                    ->preload(),
            ]);
    }
}
