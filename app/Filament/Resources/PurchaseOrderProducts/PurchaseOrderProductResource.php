<?php

namespace App\Filament\Resources\PurchaseOrderProducts;

use App\Filament\Resources\PurchaseOrderProducts\Pages\CreatePurchaseOrderProduct;
use App\Filament\Resources\PurchaseOrderProducts\Pages\EditPurchaseOrderProduct;
use App\Filament\Resources\PurchaseOrderProducts\Pages\ListPurchaseOrderProducts;
use App\Filament\Resources\PurchaseOrderProducts\Schemas\PurchaseOrderProductForm;
use App\Filament\Resources\PurchaseOrderProducts\Tables\PurchaseOrderProductsTable;
use App\Models\PurchaseOrderProduct;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use UnitEnum;

class PurchaseOrderProductResource extends Resource
{
    protected static ?string $model = PurchaseOrderProduct::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function getLabel(): ?string
    {
        return __('Purchase Order Product');
    }

    public static function getPluralLabel(): ?string
    {
        return __('Purchase Order Products');
    }

    public static function getNavigationGroup(): string|UnitEnum|null
    {
        return __('Purchasing');
    }

    public static function form(Schema $schema): Schema
    {
        return PurchaseOrderProductForm::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return PurchaseOrderProductsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListPurchaseOrderProducts::route('/'),
            'create' => CreatePurchaseOrderProduct::route('/create'),
            'edit' => EditPurchaseOrderProduct::route('/{record}/edit'),
        ];
    }

    public static function getRecordRouteBindingEloquentQuery(): Builder
    {
        return parent::getRecordRouteBindingEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
