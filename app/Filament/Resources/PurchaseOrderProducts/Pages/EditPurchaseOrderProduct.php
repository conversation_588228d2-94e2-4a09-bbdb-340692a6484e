<?php

namespace App\Filament\Resources\PurchaseOrderProducts\Pages;

use App\Filament\Resources\PurchaseOrderProducts\PurchaseOrderProductResource;
use Filament\Actions\DeleteAction;
use Filament\Actions\ForceDeleteAction;
use Filament\Actions\RestoreAction;
use Filament\Resources\Pages\EditRecord;

class EditPurchaseOrderProduct extends EditRecord
{
    protected static string $resource = PurchaseOrderProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make(),
            ForceDeleteAction::make(),
            RestoreAction::make(),
        ];
    }
}
