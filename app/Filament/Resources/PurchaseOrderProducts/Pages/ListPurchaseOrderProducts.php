<?php

namespace App\Filament\Resources\PurchaseOrderProducts\Pages;

use App\Filament\Resources\PurchaseOrderProducts\PurchaseOrderProductResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListPurchaseOrderProducts extends ListRecords
{
    protected static string $resource = PurchaseOrderProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
