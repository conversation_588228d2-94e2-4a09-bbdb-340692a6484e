<?php

namespace App\Filament\Resources\PurchaseOrderProducts\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Schemas\Components\PriceField;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class PurchaseOrderProductForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                BelongsToSelect::make('purchase_order_id')
                    ->label(__('Purchase Order'))
                    ->relationship('purchaseOrder', 'id')
                    ->required()
                    ->preload(),

                BelongsToSelect::make('product_id')
                    ->label(__('Product'))
                    ->relationship('product', 'name')
                    ->required()
                    ->preload(),

                TextInput::make('quantity')
                    ->label(__('Quantity'))
                    ->numeric()
                    ->required()
                    ->minValue(0),

                PriceField::make()
                    ->priceFieldName('buy_price')
                    ->currencyFieldName('buy_price_currency')
                    ->priceFieldLabel(__('Buy Price'))
                    ->currencyFieldLabel(__('Currency'))
                    ->required(),
            ]);
    }
}
