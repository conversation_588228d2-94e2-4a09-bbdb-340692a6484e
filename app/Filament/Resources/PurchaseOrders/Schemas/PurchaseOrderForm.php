<?php

namespace App\Filament\Resources\PurchaseOrders\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use App\Filament\Schemas\Components\PriceField;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Repeater\TableColumn;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class PurchaseOrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                BelongsToSelect::make('supplier_id')
                    ->label(__('Supplier'))
                    ->relationship('supplier', 'name')
                    ->required()
                    ->preload(),

                Repeater::make('purchaseOrderProducts')
                    ->label(__('Purchase Order Products'))
                    ->relationship('purchaseOrderProducts')
                    ->schema([
                        BelongsToSelect::make('product_id')
                            ->label(__('Product'))
                            ->relationship('product', 'name')
                            ->required()
                            ->preload(),

                        TextInput::make('quantity')
                            ->label(__('Quantity'))
                            ->numeric()
                            ->required()
                            ->minValue(0),

                        PriceField::make()
                            ->priceFieldName('buy_price')
                            ->currencyFieldName('buy_price_currency')
                            ->priceFieldLabel(__('Buy Price'))
                            ->currencyFieldLabel(__('Currency'))
                            ->required(),
                    ])
                    ->table([
                        TableColumn::make(__('Product'))
                            ->markAsRequired(),
                        TableColumn::make(__('Quantity'))
                            ->markAsRequired(),
                        TableColumn::make(__('Buy Price'))
                            ->markAsRequired(),
                        TableColumn::make(__('Currency'))
                            ->markAsRequired(),
                    ])
                    ->collapsible()
                    ->itemLabel(fn (array $state): ?string => $state['product_id'] ? \App\Models\Product::find($state['product_id'])?->name : null),
            ]);
    }
}
