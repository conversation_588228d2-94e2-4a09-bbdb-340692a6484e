<?php

namespace App\Filament\Resources\PurchaseOrders\Schemas;

use App\Filament\Forms\Components\BelongsToSelect;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class PurchaseOrderForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->columns(1)
            ->components([
                BelongsToSelect::make('supplier_id')
                    ->label(__('Supplier'))
                    ->relationship('supplier', 'name')
                    ->required()
                    ->preload(),

                Repeater::make('purchaseOrderProducts')
                    ->label(__('Purchase Order Products'))
                    ->relationship('purchaseOrderProducts')
                    ->columns(3)
                    ->schema([
                        BelongsToSelect::make('product_id')
                            ->label(__('Product'))
                            ->relationship('product', 'name')
                            ->required()
                            ->preload(),

                        TextInput::make('quantity')
                            ->label(__('Quantity'))
                            ->numeric()
                            ->required()
                            ->minValue(0),

                        TextInput::make('buy_price')
                            ->label(__('Buy Price'))
                            ->numeric()
                            ->required()
                            ->minValue(0),
                    ])
                    ->collapsible(),
            ]);
    }
}
